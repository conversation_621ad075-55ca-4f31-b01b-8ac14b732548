package corp.jamaro.jamaroservidor.app.caja.dto;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

/**
 * DTO para recibir parámetros de búsqueda de Sales completadas.
 * Todos los campos son opcionales para permitir búsquedas flexibles.
 */
@Data
public class SaleBusquedaRequest {
    
    /**
     * ID específico del Sale a buscar
     */
    private UUID saleId;
    
    /**
     * ID del User que inició la venta
     */
    private UUID iniciadaPorId;
    
    /**
     * DNI, RUC u otro documento del Cliente
     */
    private String datoDocumento;
    
    /**
     * Nombre, apellido o razón social del Cliente (búsqueda parcial)
     */
    private String nombreCliente;
    
    /**
     * Código compuesto del Item (búsqueda parcial)
     */
    private String codCompuesto;
    
    /**
     * Tipo de venta
     */
    private String tipoVenta;

    /**
     * Fecha límite para filtrar Sales (Sales con createdAt <= fechaLimite)
     * Si es null, no se aplica filtro de fecha
     */
    private Instant fechaLimite;
}
