package corp.jamaro.jamaroservidor.app.caja.dto;

import lombok.Data;

import java.util.UUID;

/**
 * DTO para inicializar una CajaDineroDigital
 */
@Data
public class InitializeCajaDineroDigitalRequest {
    
    /**
     * ID de la CajaGui existente (obligatorio)
     */
    private UUID cajaGuiId;
    
    /**
     * Detalles de la cuenta digital (obligatorio)
     */
    private String cuentaDigitalAsignada;
    
    /**
     * Monto inicial digital (obligatorio)
     */
    private Double montoInicialDigital;
}
