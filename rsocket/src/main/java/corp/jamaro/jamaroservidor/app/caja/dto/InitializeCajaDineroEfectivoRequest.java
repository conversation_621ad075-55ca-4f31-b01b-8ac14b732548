package corp.jamaro.jamaroservidor.app.caja.dto;

import lombok.Data;

import java.util.UUID;

/**
 * DTO para inicializar una CajaDineroEfectivo
 */
@Data
public class InitializeCajaDineroEfectivoRequest {
    
    /**
     * ID de la CajaGui existente (obligatorio)
     */
    private UUID cajaGuiId;
    
    /**
     * Nombre para la CajaDineroEfectivo (obligatorio)
     */
    private String nombre;
    
    /**
     * Monto inicial en efectivo (opcional si se proporcionan detalles)
     */
    private Double montoInicialEfectivo;
    
    // Detalles de denominaciones (todos opcionales)
    private Integer diezCentimos;
    private Integer veinteCentimos;
    private Integer cincuentaCentimos;
    private Integer unSol;
    private Integer dosSoles;
    private Integer cincoSoles;
    private Integer diezSoles;
    private Integer veinteSoles;
    private Integer cincuentaSoles;
    private Integer cienSoles;
    private Integer doscientosSoles;
}
