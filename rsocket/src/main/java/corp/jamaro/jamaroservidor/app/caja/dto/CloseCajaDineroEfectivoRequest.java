package corp.jamaro.jamaroservidor.app.caja.dto;

import lombok.Data;

import java.util.UUID;

/**
 * DTO para cerrar una CajaDineroEfectivo
 */
@Data
public class CloseCajaDineroEfectivoRequest {
    
    /**
     * ID de la CajaGui (obligatorio)
     */
    private UUID cajaGuiId;
    
    /**
     * ID de la CajaDineroEfectivo a cerrar (obligatorio)
     */
    private UUID cajaDineroEfectivoId;
    
    // Cantidades de cierre por denominación
    private Integer cierreDiezCentimos;
    private Integer cierreVeinteCentimos;
    private Integer cierreCincuentaCentimos;
    private Integer cierreUnSol;
    private Integer cierreDosSoles;
    private Integer cierreCincoSoles;
    private Integer cierreDiezSoles;
    private Integer cierreVeinteSoles;
    private Integer cierreCincuentaSoles;
    private Integer cierreCienSoles;
    private Integer cierreDoscientosSoles;
}
