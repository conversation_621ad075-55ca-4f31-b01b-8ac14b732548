package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroDigital;
import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroEfectivo;
import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaDigitalRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaEfectivoRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.gui.CajaGuiRepository;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class CajaGuiService {

    private final TransactionalOperator transactionalOperator;
    private final CajaGuiRepository cajaGuiRepository;
    private final CajaEfectivoRepository cajaEfectivoRepository;
    private final CajaDigitalRepository cajaDigitalRepository;

    private static final double TOLERANCE = 0.01;//tolerancia de diferencias en dinero

    /**
     * Calculate total amount from denomination details
     * 
     * @param diezCentimos Number of 10 cent coins
     * @param veinteCentimos Number of 20 cent coins
     * @param cincuentaCentimos Number of 50 cent coins
     * @param unSol Number of 1 sol coins
     * @param dosSoles Number of 2 sol coins
     * @param cincoSoles Number of 5 sol coins
     * @param diezSoles Number of 10 sol bills
     * @param veinteSoles Number of 20 sol bills
     * @param cincuentaSoles Number of 50 sol bills
     * @param cienSoles Number of 100 sol bills
     * @param doscientosSoles Number of 200 sol bills
     * @return Total amount calculated from the details
     */
    private double calculateTotalFromDetails(
            Integer diezCentimos,
            Integer veinteCentimos,
            Integer cincuentaCentimos,
            Integer unSol,
            Integer dosSoles,
            Integer cincoSoles,
            Integer diezSoles,
            Integer veinteSoles,
            Integer cincuentaSoles,
            Integer cienSoles,
            Integer doscientosSoles) {

        double total = 0.0;
        total += (diezCentimos != null ? diezCentimos * 0.1 : 0);
        total += (veinteCentimos != null ? veinteCentimos * 0.2 : 0);
        total += (cincuentaCentimos != null ? cincuentaCentimos * 0.5 : 0);
        total += (unSol != null ? unSol * 1.0 : 0);
        total += (dosSoles != null ? dosSoles * 2.0 : 0);
        total += (cincoSoles != null ? cincoSoles * 5.0 : 0);
        total += (diezSoles != null ? diezSoles * 10.0 : 0);
        total += (veinteSoles != null ? veinteSoles * 20.0 : 0);
        total += (cincuentaSoles != null ? cincuentaSoles * 50.0 : 0);
        total += (cienSoles != null ? cienSoles * 100.0 : 0);
        total += (doscientosSoles != null ? doscientosSoles * 200.0 : 0);

        return total;
    }

    /**
     * Crea una nueva CajaGui
     *
     * @param nombreCaja Nombre de la caja (obligatorio)
     * @param guiConfig Configuración GUI opcional (JSON)
     * @return Mono<CajaGui> La caja GUI creada
     */
    public Mono<CajaGui> createCajaGui(String nombreCaja, String guiConfig) {
        log.info("Creando nueva CajaGui con nombre: {}", nombreCaja);

        if (nombreCaja == null || nombreCaja.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("El nombre de la caja es obligatorio"));
        }

        CajaGui cajaGui = new CajaGui();
        cajaGui.setNombreCaja(nombreCaja.trim());
        cajaGui.setGuiConfig(guiConfig);
        cajaGui.setCreatedAt(Instant.now());

        return cajaGuiRepository.save(cajaGui)
                .doOnSuccess(saved -> log.info("CajaGui creada exitosamente con ID: {}", saved.getId()))
                .doOnError(error -> log.error("Error al crear CajaGui: {}", error.getMessage()));
    }

    /**
     * Inicializa una CajaDineroEfectivo para una CajaGui existente
     *
     * @param cajaGuiId ID de la CajaGui existente (obligatorio)
     * @param nombre Nombre para la CajaDineroEfectivo (obligatorio)
     * @param montoInicialEfectivo Monto inicial en efectivo (opcional si se proporcionan detalles)
     * @param diezCentimos Cantidad de monedas de 10 céntimos
     * @param veinteCentimos Cantidad de monedas de 20 céntimos
     * @param cincuentaCentimos Cantidad de monedas de 50 céntimos
     * @param unSol Cantidad de monedas de 1 sol
     * @param dosSoles Cantidad de monedas de 2 soles
     * @param cincoSoles Cantidad de monedas de 5 soles
     * @param diezSoles Cantidad de billetes de 10 soles
     * @param veinteSoles Cantidad de billetes de 20 soles
     * @param cincuentaSoles Cantidad de billetes de 50 soles
     * @param cienSoles Cantidad de billetes de 100 soles
     * @param doscientosSoles Cantidad de billetes de 200 soles
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroEfectivo
     */
    public Mono<CajaGui> initializeCajaDineroEfectivo(
            UUID cajaGuiId,
            String nombre,
            Double montoInicialEfectivo,
            Integer diezCentimos,
            Integer veinteCentimos,
            Integer cincuentaCentimos,
            Integer unSol,
            Integer dosSoles,
            Integer cincoSoles,
            Integer diezSoles,
            Integer veinteSoles,
            Integer cincuentaSoles,
            Integer cienSoles,
            Integer doscientosSoles) {

        log.info("Inicializando CajaDineroEfectivo para CajaGui ID: {}", cajaGuiId);

        if (cajaGuiId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaGui es obligatorio"));
        }

        if (nombre == null || nombre.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("El nombre de la caja de efectivo es obligatorio"));
        }

        return cajaGuiRepository.findById(cajaGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CajaGui no encontrada con ID: " + cajaGuiId)))
                .flatMap(cajaGui -> {
                    if (cajaGui.getCajaDineroEfectivo() != null) {
                        return Mono.error(new IllegalStateException("La CajaGui ya tiene una CajaDineroEfectivo inicializada"));
                    }

                    return SecurityUtils.getCurrentUser()
                            .switchIfEmpty(Mono.error(new IllegalStateException("Usuario no autenticado")))
                            .flatMap(user -> createCajaDineroEfectivo(
                                    nombre, montoInicialEfectivo, user,
                                    diezCentimos, veinteCentimos, cincuentaCentimos,
                                    unSol, dosSoles, cincoSoles,
                                    diezSoles, veinteSoles, cincuentaSoles,
                                    cienSoles, doscientosSoles))
                            .flatMap(cajaDineroEfectivo -> {
                                cajaGui.setCajaDineroEfectivo(cajaDineroEfectivo);
                                return cajaGuiRepository.save(cajaGui);
                            });
                })
                .doOnSuccess(saved -> log.info("CajaDineroEfectivo inicializada exitosamente para CajaGui ID: {}", cajaGuiId))
                .doOnError(error -> log.error("Error al inicializar CajaDineroEfectivo: {}", error.getMessage()));
    }

    /**
     * Crea una nueva CajaDineroEfectivo con validación de montos (sin persistir)
     */
    private Mono<CajaDineroEfectivo> createCajaDineroEfectivo(
            String nombre, Double montoInicialEfectivo, User user,
            Integer diezCentimos, Integer veinteCentimos, Integer cincuentaCentimos,
            Integer unSol, Integer dosSoles, Integer cincoSoles,
            Integer diezSoles, Integer veinteSoles, Integer cincuentaSoles,
            Integer cienSoles, Integer doscientosSoles) {

        // Calcular total desde detalles si se proporcionan
        boolean hasDetails = hasAnyDetail(diezCentimos, veinteCentimos, cincuentaCentimos,
                unSol, dosSoles, cincoSoles, diezSoles, veinteSoles,
                cincuentaSoles, cienSoles, doscientosSoles);

        double calculatedTotal = 0.0;
        if (hasDetails) {
            calculatedTotal = calculateTotalFromDetails(
                    diezCentimos, veinteCentimos, cincuentaCentimos,
                    unSol, dosSoles, cincoSoles,
                    diezSoles, veinteSoles, cincuentaSoles,
                    cienSoles, doscientosSoles);
        }

        // Validar montos
        Double finalAmount;
        if (montoInicialEfectivo != null && hasDetails) {
            // Ambos proporcionados: verificar tolerancia
            double difference = Math.abs(montoInicialEfectivo - calculatedTotal);
            if (difference > TOLERANCE) {
                return Mono.error(new IllegalArgumentException(
                        String.format("El monto inicial (%.2f) no coincide con el total calculado (%.2f). Diferencia: %.2f",
                                montoInicialEfectivo, calculatedTotal, difference)));
            }
            finalAmount = montoInicialEfectivo;
        } else if (montoInicialEfectivo != null) {
            // Solo monto inicial proporcionado
            finalAmount = montoInicialEfectivo;
        } else if (hasDetails) {
            // Solo detalles proporcionados
            finalAmount = calculatedTotal;
        } else {
            return Mono.error(new IllegalArgumentException(
                    "Debe proporcionar al menos el monto inicial o los detalles de denominaciones"));
        }

        // Redondear a 2 decimales
        finalAmount = BigDecimal.valueOf(finalAmount)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        CajaDineroEfectivo cajaDineroEfectivo = new CajaDineroEfectivo();
        cajaDineroEfectivo.setNombre(nombre.trim());
        cajaDineroEfectivo.setAbiertaPor(user);
        cajaDineroEfectivo.setAbiertaEl(Instant.now());
        cajaDineroEfectivo.setMontoInicialEfectivo(finalAmount);

        // Establecer detalles de inicio si se proporcionaron
        if (hasDetails) {
            cajaDineroEfectivo.setInicioDiezCentimos(diezCentimos);
            cajaDineroEfectivo.setInicioVeinteCentimos(veinteCentimos);
            cajaDineroEfectivo.setInicioCincuentaCentimos(cincuentaCentimos);
            cajaDineroEfectivo.setInicioUnSol(unSol);
            cajaDineroEfectivo.setInicioDosSoles(dosSoles);
            cajaDineroEfectivo.setInicioCincoSoles(cincoSoles);
            cajaDineroEfectivo.setInicioDiezSoles(diezSoles);
            cajaDineroEfectivo.setInicioVeinteSoles(veinteSoles);
            cajaDineroEfectivo.setInicioCincuentaSoles(cincuentaSoles);
            cajaDineroEfectivo.setInicioCienSoles(cienSoles);
            cajaDineroEfectivo.setInicioDoscientosSoles(doscientosSoles);
        }

        // Inicializar campos de cálculo en 0.0
        cajaDineroEfectivo.setTotalEntradasEfectivo(0.0);
        cajaDineroEfectivo.setTotalSalidasEfectivo(0.0);
        cajaDineroEfectivo.setPagosVentaContadoEfectivo(0.0);
        cajaDineroEfectivo.setPagosVentaCreditoEfectivo(0.0);
        cajaDineroEfectivo.setPagosVentaPedidoEfectivo(0.0);
        cajaDineroEfectivo.setDevolucionesVentaEfectivo(0.0);
        cajaDineroEfectivo.setIngresosExtraEfectivo(0.0);
        cajaDineroEfectivo.setGastosExtraEfectivo(0.0);
        cajaDineroEfectivo.setPagosDineroProgramadoEfectivo(0.0);
        cajaDineroEfectivo.setEstaCerrada(false);

        return Mono.just(cajaDineroEfectivo);
    }

    /**
     * Verifica si se proporcionó algún detalle de denominación
     */
    private boolean hasAnyDetail(Integer... details) {
        for (Integer detail : details) {
            if (detail != null && detail > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * Inicializa una CajaDineroDigital para una CajaGui existente
     *
     * @param cajaGuiId ID de la CajaGui existente (obligatorio)
     * @param cuentaDigitalAsignada Detalles de la cuenta digital (obligatorio)
     * @param montoInicialDigital Monto inicial digital (obligatorio)
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroDigital
     */
    public Mono<CajaGui> initializeCajaDineroDigital(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital) {

        log.info("Inicializando CajaDineroDigital para CajaGui ID: {}", cajaGuiId);

        if (cajaGuiId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaGui es obligatorio"));
        }

        if (cuentaDigitalAsignada == null || cuentaDigitalAsignada.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("La cuenta digital asignada es obligatoria"));
        }

        if (montoInicialDigital == null) {
            return Mono.error(new IllegalArgumentException("El monto inicial digital es obligatorio"));
        }

        if (montoInicialDigital < 0) {
            return Mono.error(new IllegalArgumentException("El monto inicial digital no puede ser negativo"));
        }

        return cajaGuiRepository.findById(cajaGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CajaGui no encontrada con ID: " + cajaGuiId)))
                .flatMap(cajaGui -> {
                    if (cajaGui.getCajaDineroDigital() != null) {
                        return Mono.error(new IllegalStateException("La CajaGui ya tiene una CajaDineroDigital inicializada"));
                    }

                    return SecurityUtils.getCurrentUser()
                            .switchIfEmpty(Mono.error(new IllegalStateException("Usuario no autenticado")))
                            .flatMap(user -> createCajaDineroDigital(cuentaDigitalAsignada, montoInicialDigital, user))
                            .flatMap(cajaDineroDigital -> {
                                cajaGui.setCajaDineroDigital(cajaDineroDigital);
                                return cajaGuiRepository.save(cajaGui);
                            });
                })
                .doOnSuccess(saved -> log.info("CajaDineroDigital inicializada exitosamente para CajaGui ID: {}", cajaGuiId))
                .doOnError(error -> log.error("Error al inicializar CajaDineroDigital: {}", error.getMessage()));
    }

    /**
     * Crea una nueva CajaDineroDigital
     */
    private Mono<CajaDineroDigital> createCajaDineroDigital(
            String cuentaDigitalAsignada, Double montoInicialDigital, User user) {

        // Redondear a 2 decimales
        Double finalAmount = BigDecimal.valueOf(montoInicialDigital)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        CajaDineroDigital cajaDineroDigital = new CajaDineroDigital();
        cajaDineroDigital.setCuentaDigitalAsignada(cuentaDigitalAsignada.trim());
        cajaDineroDigital.setAbiertaPor(user);
        cajaDineroDigital.setAbiertaEl(Instant.now());
        cajaDineroDigital.setMontoInicialDigital(finalAmount);

        // Inicializar campos de cálculo en 0.0
        cajaDineroDigital.setTotalEntradasDigital(0.0);
        cajaDineroDigital.setTotalSalidasDigital(0.0);
        cajaDineroDigital.setPagosVentaContadoDigital(0.0);
        cajaDineroDigital.setPagosVentaCreditoDigital(0.0);
        cajaDineroDigital.setPagosVentaPedidoDigital(0.0);
        cajaDineroDigital.setDevolucionesVentaDigital(0.0);
        cajaDineroDigital.setIngresosExtraDigital(0.0);
        cajaDineroDigital.setGastosExtraDigital(0.0);
        cajaDineroDigital.setPagosDineroProgramadoDigital(0.0);
        cajaDineroDigital.setEstaCerrada(false);

        return cajaDigitalRepository.save(cajaDineroDigital);
    }

    /**
     * Cierra una CajaDineroEfectivo calculando los montos de cierre
     *
     * @param cajaGuiId ID de la CajaGui
     * @param cajaDineroEfectivoId ID de la CajaDineroEfectivo a cerrar
     * @param cierreDiezCentimos Cantidad de monedas de 10 céntimos en el cierre
     * @param cierreVeinteCentimos Cantidad de monedas de 20 céntimos en el cierre
     * @param cierreCincuentaCentimos Cantidad de monedas de 50 céntimos en el cierre
     * @param cierreUnSol Cantidad de monedas de 1 sol en el cierre
     * @param cierreDosSoles Cantidad de monedas de 2 soles en el cierre
     * @param cierreCincoSoles Cantidad de monedas de 5 soles en el cierre
     * @param cierreDiezSoles Cantidad de billetes de 10 soles en el cierre
     * @param cierreVeinteSoles Cantidad de billetes de 20 soles en el cierre
     * @param cierreCincuentaSoles Cantidad de billetes de 50 soles en el cierre
     * @param cierreCienSoles Cantidad de billetes de 100 soles en el cierre
     * @param cierreDoscientosSoles Cantidad de billetes de 200 soles en el cierre
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    public Mono<CajaGui> closeCajaDineroEfectivo(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles) {

        log.info("Cerrando CajaDineroEfectivo ID: {} para CajaGui ID: {}", cajaDineroEfectivoId, cajaGuiId);

        if (cajaGuiId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaGui es obligatorio"));
        }

        if (cajaDineroEfectivoId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaDineroEfectivo es obligatorio"));
        }

        return transactionalOperator.transactional(
            cajaGuiRepository.findById(cajaGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CajaGui no encontrada con ID: " + cajaGuiId)))
                .flatMap(cajaGui -> {
                    if (cajaGui.getCajaDineroEfectivo() == null) {
                        return Mono.error(new IllegalStateException("La CajaGui no tiene una CajaDineroEfectivo inicializada"));
                    }

                    if (!cajaGui.getCajaDineroEfectivo().getId().equals(cajaDineroEfectivoId)) {
                        return Mono.error(new IllegalArgumentException("El ID de CajaDineroEfectivo no coincide con la CajaGui"));
                    }

                    if (Boolean.TRUE.equals(cajaGui.getCajaDineroEfectivo().getEstaCerrada())) {
                        return Mono.error(new IllegalStateException("La CajaDineroEfectivo ya está cerrada"));
                    }

                    return SecurityUtils.getCurrentUser()
                            .switchIfEmpty(Mono.error(new IllegalStateException("Usuario no autenticado")))
                            .flatMap(user -> processCierreEfectivo(cajaGui.getCajaDineroEfectivo(), user,
                                    cierreDiezCentimos, cierreVeinteCentimos, cierreCincuentaCentimos,
                                    cierreUnSol, cierreDosSoles, cierreCincoSoles,
                                    cierreDiezSoles, cierreVeinteSoles, cierreCincuentaSoles,
                                    cierreCienSoles, cierreDoscientosSoles))
                            .flatMap(cajaDineroEfectivo -> {
                                cajaGui.setCajaDineroEfectivo(cajaDineroEfectivo);
                                return cajaGuiRepository.save(cajaGui);
                            });
                })
        )
        .doOnSuccess(saved -> log.info("CajaDineroEfectivo cerrada exitosamente para CajaGui ID: {}", cajaGuiId))
        .doOnError(error -> log.error("Error al cerrar CajaDineroEfectivo: {}", error.getMessage()));
    }

    /**
     * Procesa el cierre de una CajaDineroEfectivo
     */
    private Mono<CajaDineroEfectivo> processCierreEfectivo(
            CajaDineroEfectivo cajaDineroEfectivo, User user,
            Integer cierreDiezCentimos, Integer cierreVeinteCentimos, Integer cierreCincuentaCentimos,
            Integer cierreUnSol, Integer cierreDosSoles, Integer cierreCincoSoles,
            Integer cierreDiezSoles, Integer cierreVeinteSoles, Integer cierreCincuentaSoles,
            Integer cierreCienSoles, Integer cierreDoscientosSoles) {

        // Calcular el cierre declarado desde los detalles de denominaciones
        double cierreDeclarado = calculateTotalFromDetails(
                cierreDiezCentimos, cierreVeinteCentimos, cierreCincuentaCentimos,
                cierreUnSol, cierreDosSoles, cierreCincoSoles,
                cierreDiezSoles, cierreVeinteSoles, cierreCincuentaSoles,
                cierreCienSoles, cierreDoscientosSoles);

        // Redondear a 2 decimales
        cierreDeclarado = BigDecimal.valueOf(cierreDeclarado)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Calcular el cierre calculado: totalEntradasEfectivo - totalSalidasEfectivo + montoInicialEfectivo
        double totalEntradas = cajaDineroEfectivo.getTotalEntradasEfectivo() != null ?
                cajaDineroEfectivo.getTotalEntradasEfectivo() : 0.0;
        double totalSalidas = cajaDineroEfectivo.getTotalSalidasEfectivo() != null ?
                cajaDineroEfectivo.getTotalSalidasEfectivo() : 0.0;
        double montoInicial = cajaDineroEfectivo.getMontoInicialEfectivo() != null ?
                cajaDineroEfectivo.getMontoInicialEfectivo() : 0.0;

        double cierreCalculado = totalEntradas - totalSalidas + montoInicial;
        cierreCalculado = BigDecimal.valueOf(cierreCalculado)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Calcular diferencia: cierreCalculado - cierreDeclarado
        double diferencia = cierreCalculado - cierreDeclarado;
        diferencia = BigDecimal.valueOf(diferencia)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Actualizar campos de cierre
        cajaDineroEfectivo.setCerradaPor(user);
        cajaDineroEfectivo.setCerradaEl(Instant.now());
        cajaDineroEfectivo.setCierreEfectivoCalculado(cierreCalculado);
        cajaDineroEfectivo.setCierreEfectivoDeclarado(cierreDeclarado);
        cajaDineroEfectivo.setDiferenciaCierreEfectivo(diferencia);
        cajaDineroEfectivo.setEstaCerrada(true);

        // Establecer detalles de cierre
        cajaDineroEfectivo.setCierreDiezCentimos(cierreDiezCentimos);
        cajaDineroEfectivo.setCierreVeinteCentimos(cierreVeinteCentimos);
        cajaDineroEfectivo.setCierreCincuentaCentimos(cierreCincuentaCentimos);
        cajaDineroEfectivo.setCierreUnSol(cierreUnSol);
        cajaDineroEfectivo.setCierreDosSoles(cierreDosSoles);
        cajaDineroEfectivo.setCierreCincoSoles(cierreCincoSoles);
        cajaDineroEfectivo.setCierreDiezSoles(cierreDiezSoles);
        cajaDineroEfectivo.setCierreVeinteSoles(cierreVeinteSoles);
        cajaDineroEfectivo.setCierreCincuentaSoles(cierreCincuentaSoles);
        cajaDineroEfectivo.setCierreCienSoles(cierreCienSoles);
        cajaDineroEfectivo.setCierreDoscientosSoles(cierreDoscientosSoles);

        return cajaEfectivoRepository.save(cajaDineroEfectivo);
    }

    /**
     * Cierra una CajaDineroDigital calculando los montos de cierre
     *
     * @param cajaGuiId ID de la CajaGui
     * @param cajaDineroDigitalId ID de la CajaDineroDigital a cerrar
     * @param cierreDigitalDeclarado Monto declarado por el usuario para el cierre
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    public Mono<CajaGui> closeCajaDineroDigital(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado) {

        log.info("Cerrando CajaDineroDigital ID: {} para CajaGui ID: {}", cajaDineroDigitalId, cajaGuiId);

        if (cajaGuiId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaGui es obligatorio"));
        }

        if (cajaDineroDigitalId == null) {
            return Mono.error(new IllegalArgumentException("El ID de CajaDineroDigital es obligatorio"));
        }

        if (cierreDigitalDeclarado == null) {
            return Mono.error(new IllegalArgumentException("El cierre digital declarado es obligatorio"));
        }

        if (cierreDigitalDeclarado < 0) {
            return Mono.error(new IllegalArgumentException("El cierre digital declarado no puede ser negativo"));
        }

        return transactionalOperator.transactional(
            cajaGuiRepository.findById(cajaGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CajaGui no encontrada con ID: " + cajaGuiId)))
                .flatMap(cajaGui -> {
                    if (cajaGui.getCajaDineroDigital() == null) {
                        return Mono.error(new IllegalStateException("La CajaGui no tiene una CajaDineroDigital inicializada"));
                    }

                    if (!cajaGui.getCajaDineroDigital().getId().equals(cajaDineroDigitalId)) {
                        return Mono.error(new IllegalArgumentException("El ID de CajaDineroDigital no coincide con la CajaGui"));
                    }

                    if (Boolean.TRUE.equals(cajaGui.getCajaDineroDigital().getEstaCerrada())) {
                        return Mono.error(new IllegalStateException("La CajaDineroDigital ya está cerrada"));
                    }

                    return SecurityUtils.getCurrentUser()
                            .switchIfEmpty(Mono.error(new IllegalStateException("Usuario no autenticado")))
                            .flatMap(user -> processCierreDigital(cajaGui.getCajaDineroDigital(), user, cierreDigitalDeclarado))
                            .flatMap(cajaDineroDigital -> {
                                cajaGui.setCajaDineroDigital(cajaDineroDigital);
                                return cajaGuiRepository.save(cajaGui);
                            });
                })
        )
        .doOnSuccess(saved -> log.info("CajaDineroDigital cerrada exitosamente para CajaGui ID: {}", cajaGuiId))
        .doOnError(error -> log.error("Error al cerrar CajaDineroDigital: {}", error.getMessage()));
    }

    /**
     * Procesa el cierre de una CajaDineroDigital
     */
    private Mono<CajaDineroDigital> processCierreDigital(
            CajaDineroDigital cajaDineroDigital, User user, Double cierreDigitalDeclarado) {

        // Redondear el cierre declarado a 2 decimales
        cierreDigitalDeclarado = BigDecimal.valueOf(cierreDigitalDeclarado)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Calcular el cierre calculado: totalEntradasDigital - totalSalidasDigital + montoInicialDigital
        double totalEntradas = cajaDineroDigital.getTotalEntradasDigital() != null ?
                cajaDineroDigital.getTotalEntradasDigital() : 0.0;
        double totalSalidas = cajaDineroDigital.getTotalSalidasDigital() != null ?
                cajaDineroDigital.getTotalSalidasDigital() : 0.0;
        double montoInicial = cajaDineroDigital.getMontoInicialDigital() != null ?
                cajaDineroDigital.getMontoInicialDigital() : 0.0;

        double cierreCalculado = totalEntradas - totalSalidas + montoInicial;
        cierreCalculado = BigDecimal.valueOf(cierreCalculado)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Calcular diferencia: cierreCalculado - cierreDeclarado
        double diferencia = cierreCalculado - cierreDigitalDeclarado;
        diferencia = BigDecimal.valueOf(diferencia)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();

        // Actualizar campos de cierre
        cajaDineroDigital.setCerradaPor(user);
        cajaDineroDigital.setCerradaEl(Instant.now());
        cajaDineroDigital.setCierreDigitalCalculado(cierreCalculado);
        cajaDineroDigital.setCierreDigitalDeclarado(cierreDigitalDeclarado);
        cajaDineroDigital.setDiferenciaCierreDigital(diferencia);
        cajaDineroDigital.setEstaCerrada(true);

        return cajaDigitalRepository.save(cajaDineroDigital);
    }
}
