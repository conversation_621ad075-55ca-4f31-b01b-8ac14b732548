package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.service.CajaGuiService;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

import java.util.List;

@Controller
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
public class CajaGuiController {

    private final CajaGuiService cajaGuiService;

    /**
     * Permite a los clientes suscribirse a actualizaciones de la lista de Sales pendientes de cobro.
     *
     * Los clientes recibirán:
     * 1. La lista actual de Sales con estaPagadoEntregado = false
     * 2. Actualizaciones en tiempo real cuando cambien los Sales pendientes
     *
     * Los Sales se ordenan por createdAt de más reciente a más antiguo.
     *
     * @return Flux que emite listas de Sales pendientes de cobro
     */
    @MessageMapping("cajaGui.subscribeToSalesPorCobrar")
    public Flux<List<Sale>> subscribeToSalesPorCobrar() {
        log.info("Cliente suscribiéndose a Sales por cobrar");
        return cajaGuiService.subscribeToSalesPorCobrar();
    }
}
