package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.dto.SaleBusquedaRequest;
import corp.jamaro.jamaroservidor.app.caja.service.CajaGuiService;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

import java.util.List;

@Controller
@RequiredArgsConstructor
@Slf4j
@MessageMapping("cajaGui")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
public class CajaGuiController {

    private final CajaGuiService cajaGuiService;

    /**
     * Permite a los clientes suscribirse a actualizaciones de la lista de Sales pendientes de cobro.
     *
     * Los clientes recibirán:
     * 1. La lista actual de Sales con estaPagadoEntregado = false
     * 2. Actualizaciones en tiempo real cuando cambien los Sales pendientes
     *
     * Los Sales se ordenan por createdAt de más reciente a más antiguo.
     *
     * @return Flux que emite listas de Sales pendientes de cobro
     */
    @MessageMapping("subscribeToSalesPorCobrar")
    public Flux<List<Sale>> subscribeToSalesPorCobrar() {
        log.info("Cliente suscribiéndose a Sales por cobrar");
        return cajaGuiService.subscribeToSalesPorCobrar();
    }

    /**
     * Busca Sales completadas (estaPagadoEntregado = true) con filtros opcionales.
     *
     * Permite filtrar por:
     * - ID específico del Sale
     * - ID del User que inició la venta
     * - Datos de documento del Cliente (DNI, RUC, otroDocumento)
     * - Nombre, apellido o razón social del Cliente (búsqueda parcial)
     * - Código compuesto del Item (búsqueda parcial)
     * - Tipo de venta
     *
     * Si no se proporcionan filtros, retorna las últimas 99 ventas completadas.
     * Si hay filtros, retorna máximo 33 resultados.
     *
     * @param request Parámetros de búsqueda (todos opcionales)
     * @return Flux<Sale> Lista de Sales que coinciden con los filtros
     */
    @MessageMapping("buscarSalesCompletadas")
    public Flux<Sale> buscarSalesCompletadas(SaleBusquedaRequest request) {
        log.info("Buscando Sales completadas con filtros: {}", request);

        // Extraer parámetros del request (pueden ser null)
        return cajaGuiService.buscarSalesCompletadas(
                request.getSaleId(),
                request.getIniciadaPorId(),
                request.getDatoDocumento(),
                request.getNombreCliente(),
                request.getCodCompuesto(),
                request.getTipoVenta()
        );
    }
}
