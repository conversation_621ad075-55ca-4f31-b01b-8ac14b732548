package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.dto.*;
import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import corp.jamaro.jamaroservidor.app.caja.service.CajaGuiService;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Controller
@RequiredArgsConstructor
@Slf4j
@MessageMapping("cajaGui")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
public class CajaGuiController {

    private final CajaGuiService cajaGuiService;

    /**
     * Permite a los clientes suscribirse a actualizaciones de la lista de Sales pendientes de cobro.
     *
     * Los clientes recibirán:
     * 1. La lista actual de Sales con estaPagadoEntregado = false
     * 2. Actualizaciones en tiempo real cuando cambien los Sales pendientes
     *
     * Los Sales se ordenan por createdAt de más reciente a más antiguo.
     *
     * @return Flux que emite listas de Sales pendientes de cobro
     */
    @MessageMapping("subscribeToSalesPorCobrar")
    public Flux<List<Sale>> subscribeToSalesPorCobrar() {
        log.info("Cliente suscribiéndose a Sales por cobrar");
        return cajaGuiService.subscribeToSalesPorCobrar();
    }

    /**
     * Busca Sales completadas (estaPagadoEntregado = true) con filtros opcionales.
     *
     * Permite filtrar por:
     * - ID específico del Sale
     * - ID del User que inició la venta
     * - Datos de documento del Cliente (DNI, RUC, otroDocumento)
     * - Nombre, apellido o razón social del Cliente (búsqueda parcial)
     * - Código compuesto del Item (búsqueda parcial)
     * - Tipo de venta
     *
     * Si no se proporcionan filtros, retorna las últimas 99 ventas completadas.
     * Si hay filtros, retorna máximo 33 resultados.
     *
     * @param request Parámetros de búsqueda (todos opcionales)
     * @return Flux<Sale> Lista de Sales que coinciden con los filtros
     */
    @MessageMapping("buscarSalesCompletadas")
    public Flux<Sale> buscarSalesCompletadas(SaleBusquedaRequest request) {
        log.info("Buscando Sales completadas con filtros: {}", request);

        // Extraer parámetros del request (pueden ser null)
        return cajaGuiService.buscarSalesCompletadas(
                request.getSaleId(),
                request.getIniciadaPorId(),
                request.getDatoDocumento(),
                request.getNombreCliente(),
                request.getCodCompuesto(),
                request.getTipoVenta(),
                request.getFechaLimite()
        );
    }

    /**
     * Permite a los clientes suscribirse a actualizaciones de la CajaGui actual.
     *
     * Los clientes recibirán:
     * 1. La CajaGui actual del usuario autenticado (si existe)
     * 2. Actualizaciones en tiempo real cuando se modifique la CajaGui
     *
     * @return Flux que emite la CajaGui actual y sus actualizaciones futuras
     */
    @MessageMapping("subscribeToCurrentCajaGui")
    public Flux<CajaGui> subscribeToCurrentCajaGui() {
        log.info("Cliente suscribiéndose a actualizaciones de CajaGui");
        return cajaGuiService.subscribeToCurrentCajaGui();
    }

    /**
     * Crea una nueva CajaGui.
     *
     * @param request Datos para crear la CajaGui
     * @return Mono<CajaGui> La CajaGui creada
     */
    @MessageMapping("createCajaGui")
    public Mono<CajaGui> createCajaGui(CreateCajaGuiRequest request) {
        log.info("Creando nueva CajaGui: {}", request.getNombreCaja());
        return cajaGuiService.createCajaGui(request.getNombreCaja(), request.getGuiConfig());
    }

    /**
     * Inicializa una CajaDineroEfectivo para una CajaGui existente.
     *
     * @param request Datos para inicializar la CajaDineroEfectivo
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroEfectivo
     */
    @MessageMapping("initializeCajaDineroEfectivo")
    public Mono<CajaGui> initializeCajaDineroEfectivo(InitializeCajaDineroEfectivoRequest request) {
        log.info("Inicializando CajaDineroEfectivo para CajaGui ID: {}", request.getCajaGuiId());

        return cajaGuiService.initializeCajaDineroEfectivo(
                request.getCajaGuiId(),
                request.getNombre(),
                request.getMontoInicialEfectivo(),
                request.getDiezCentimos(),
                request.getVeinteCentimos(),
                request.getCincuentaCentimos(),
                request.getUnSol(),
                request.getDosSoles(),
                request.getCincoSoles(),
                request.getDiezSoles(),
                request.getVeinteSoles(),
                request.getCincuentaSoles(),
                request.getCienSoles(),
                request.getDoscientosSoles()
        );
    }

    /**
     * Inicializa una CajaDineroDigital para una CajaGui existente.
     *
     * @param request Datos para inicializar la CajaDineroDigital
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroDigital
     */
    @MessageMapping("initializeCajaDineroDigital")
    public Mono<CajaGui> initializeCajaDineroDigital(InitializeCajaDineroDigitalRequest request) {
        log.info("Inicializando CajaDineroDigital para CajaGui ID: {}", request.getCajaGuiId());

        return cajaGuiService.initializeCajaDineroDigital(
                request.getCajaGuiId(),
                request.getCuentaDigitalAsignada(),
                request.getMontoInicialDigital()
        );
    }

    /**
     * Cierra una CajaDineroEfectivo con los detalles de cierre.
     *
     * @param request Datos para cerrar la CajaDineroEfectivo
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    @MessageMapping("closeCajaDineroEfectivo")
    public Mono<CajaGui> closeCajaDineroEfectivo(CloseCajaDineroEfectivoRequest request) {
        log.info("Cerrando CajaDineroEfectivo ID: {} para CajaGui ID: {}",
                request.getCajaDineroEfectivoId(), request.getCajaGuiId());

        return cajaGuiService.closeCajaDineroEfectivo(
                request.getCajaGuiId(),
                request.getCajaDineroEfectivoId(),
                request.getCierreDiezCentimos(),
                request.getCierreVeinteCentimos(),
                request.getCierreCincuentaCentimos(),
                request.getCierreUnSol(),
                request.getCierreDosSoles(),
                request.getCierreCincoSoles(),
                request.getCierreDiezSoles(),
                request.getCierreVeinteSoles(),
                request.getCierreCincuentaSoles(),
                request.getCierreCienSoles(),
                request.getCierreDoscientosSoles()
        );
    }

    /**
     * Cierra una CajaDineroDigital con el monto declarado.
     *
     * @param request Datos para cerrar la CajaDineroDigital
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    @MessageMapping("closeCajaDineroDigital")
    public Mono<CajaGui> closeCajaDineroDigital(CloseCajaDineroDigitalRequest request) {
        log.info("Cerrando CajaDineroDigital ID: {} para CajaGui ID: {}",
                request.getCajaDineroDigitalId(), request.getCajaGuiId());

        return cajaGuiService.closeCajaDineroDigital(
                request.getCajaGuiId(),
                request.getCajaDineroDigitalId(),
                request.getCierreDigitalDeclarado()
        );
    }
}
