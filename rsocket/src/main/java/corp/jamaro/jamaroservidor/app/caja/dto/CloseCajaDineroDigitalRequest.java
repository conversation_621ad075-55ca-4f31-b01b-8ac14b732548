package corp.jamaro.jamaroservidor.app.caja.dto;

import lombok.Data;

import java.util.UUID;

/**
 * DTO para cerrar una CajaDineroDigital
 */
@Data
public class CloseCajaDineroDigitalRequest {
    
    /**
     * ID de la CajaGui (obligatorio)
     */
    private UUID cajaGuiId;
    
    /**
     * ID de la CajaDineroDigital a cerrar (obligatorio)
     */
    private UUID cajaDineroDigitalId;
    
    /**
     * Monto declarado por el usuario para el cierre (obligatorio)
     */
    private Double cierreDigitalDeclarado;
}
