package corp.jamaro.jamaroservidor.app.ventas.repository;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface SaleRepository extends ReactiveNeo4jRepository<Sale, UUID> {

    /**
     * Obtiene la Sale relacionada a un CobroDineroProgramado específico
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(c:CobroDineroProgramado)
           WHERE c.id = $cobroDineroProgramadoId
           RETURN s
           """)
    Mono<Sale> findSaleByCobroDineroProgramadoId(@Param("cobroDineroProgramadoId") UUID cobroDineroProgramadoId);

    /**
     * Actualiza el Cliente de un Sale.
     * Si clienteId es null, elimina la relación con Cliente (venta genérica).
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[r:CON_CLIENTE]->(:Cliente)
           DELETE r
           WITH s
           OPTIONAL MATCH (c:Cliente) WHERE c.id = $clienteId AND $clienteId IS NOT NULL
           FOREACH (ignore IN CASE WHEN c IS NOT NULL THEN [1] ELSE [] END |
               CREATE (s)-[:CON_CLIENTE]->(c)
           )
           RETURN s.id
           """)
    Mono<UUID> updateCliente(@Param("saleId") UUID saleId, @Param("clienteId") UUID clienteId);

    /**
     * Verifica si existe un BienServicioCargado para un Item específico en un Sale.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND i.codCompuesto = $itemCodCompuesto
           RETURN count(bsc) > 0
           """)
    Mono<Boolean> existsBienServicioCargadoForItem(@Param("saleId") UUID saleId, @Param("itemCodCompuesto") String itemCodCompuesto);

    /**
     * Obtiene el precio de venta público de un Item por su código compuesto.
     * Usado cuando precioInicial es null en addItemToSale.
     */
    @Query("""
           MATCH (i:Item) WHERE i.codCompuesto = $itemCodCompuesto
           RETURN i.precioVentaPublico
           """)
    Mono<Double> getItemPrecioVentaPublico(@Param("itemCodCompuesto") String itemCodCompuesto);

    /**
     * Obtiene la descripción de un Item por su código compuesto.
     * Usado para inicializar descripcionDelBienServicio.
     */
    @Query("""
           MATCH (i:Item) WHERE i.codCompuesto = $itemCodCompuesto
           RETURN i.descripcion
           """)
    Mono<String> getItemDescripcion(@Param("itemCodCompuesto") String itemCodCompuesto);

    /**
     * Crea relación entre Sale y BienServicioCargado.
     * Usado después de crear el BienServicioCargado con el repositorio específico.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           MATCH (bsc:BienServicioCargado) WHERE bsc.id = $bienServicioCargadoId
           CREATE (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc)
           RETURN bsc.id
           """)
    Mono<UUID> createSaleBienServicioCargadoRelationship(@Param("saleId") UUID saleId, @Param("bienServicioCargadoId") UUID bienServicioCargadoId);

    /**
     * Crea relación entre BienServicioCargado e Item.
     * Usado después de crear el BienServicioCargado con el repositorio específico.
     */
    @Query("""
           MATCH (bsc:BienServicioCargado) WHERE bsc.id = $bienServicioCargadoId
           MATCH (i:Item) WHERE i.codCompuesto = $itemCodCompuesto
           CREATE (bsc)-[:CON_ITEM_CARGADO]->(i)
           RETURN i.id
           """)
    Mono<UUID> createBienServicioCargadoItemRelationship(@Param("bienServicioCargadoId") UUID bienServicioCargadoId, @Param("itemCodCompuesto") String itemCodCompuesto);

    /**
     * Actualiza la cantidad de un BienServicioCargado existente.
     * Recalcula montoAcordado = precioAcordado * nuevaCantidad
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND i.codCompuesto = $itemCodCompuesto
           SET bsc.cantidad = bsc.cantidad + $cantidadIncremento,
               bsc.montoAcordado = bsc.precioAcordado * (bsc.cantidad + $cantidadIncremento)
           RETURN bsc.id
           """)
    Mono<UUID> incrementBienServicioCargadoCantidad(@Param("saleId") UUID saleId, @Param("itemCodCompuesto") String itemCodCompuesto, @Param("cantidadIncremento") Double cantidadIncremento);

    /**
     * Actualiza los campos de un BienServicioCargado.
     * Recalcula montoAcordado = precioAcordado * cantidad
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           WHERE s.id = $saleId AND bsc.id = $bienServicioCargadoId
           SET bsc.precioAcordado = $precioAcordado,
               bsc.cantidad = CASE WHEN $cantidad IS NOT NULL THEN $cantidad ELSE bsc.cantidad END,
               bsc.descripcionDelBienServicio = CASE WHEN $descripcionDelBienServicio IS NOT NULL THEN $descripcionDelBienServicio ELSE bsc.descripcionDelBienServicio END
           WITH bsc
           SET bsc.montoAcordado = bsc.precioAcordado * bsc.cantidad
           RETURN bsc.id
           """)
    Mono<UUID> updateBienServicioCargado(@Param("saleId") UUID saleId, @Param("bienServicioCargadoId") UUID bienServicioCargadoId,
                                        @Param("precioAcordado") Double precioAcordado, @Param("cantidad") Double cantidad, @Param("descripcionDelBienServicio") String descripcionDelBienServicio);

    /**
     * Elimina un BienServicioCargado específico.
     */
    @Query("""
           MATCH (s:Sale)-[r:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND bsc.id = $bienServicioCargadoId
           DETACH DELETE bsc
           RETURN s.id
           """)
    Mono<UUID> deleteBienServicioCargado(@Param("saleId") UUID saleId, @Param("bienServicioCargadoId") UUID bienServicioCargadoId);

    /**
     * Elimina todos los BienServicioCargado de un Sale.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[r:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           DETACH DELETE bsc
           RETURN s.id
           """)
    Mono<UUID> deleteAllBienServicioCargado(@Param("saleId") UUID saleId);

    /**
     * Obtiene los IDs de todos los BienServicioCargado de un Sale.
     * Usado para trabajar con repositorios específicos y evitar problemas reactivos.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           WHERE s.id = $saleId
           RETURN bsc.id
           """)
    Flux<UUID> getBienServicioCargadoIdsBySaleId(@Param("saleId") UUID saleId);

    /**
     * Calcula y actualiza los montos totales de un Sale basado en sus BienServicioCargado.
     * totalMontoInicial = suma de (precioInicial * cantidad)
     * totalMontoAcordado = suma de (montoAcordado) = suma de (precioAcordado * cantidad)
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           WITH s,
                sum(bsc.precioInicial * bsc.cantidad) as totalInicial,
                sum(bsc.montoAcordado) as totalAcordado
           SET s.totalMontoInicial = COALESCE(totalInicial, 0.0),
               s.totalMontoAcordado = COALESCE(totalAcordado, 0.0)
           RETURN s
           """)
    Mono<Sale> calculateAndUpdateTotals(@Param("saleId") UUID saleId);

    /**
     * Verifica que exista al menos un BienServicioCargado en el Sale.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           RETURN count(bsc) > 0
           """)
    Mono<Boolean> hasBienServicioCargado(@Param("saleId") UUID saleId);

    /**
     * Actualiza los campos del Sale para iniciar venta de contado.
     * Se usa después de calcular totales y crear CobroDineroProgramado con repositorios específicos.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           SET s.tipoVenta = 'CONTADO',
               s.estaPagadoEntregado = false,
               s.totalRestante = s.totalMontoAcordado
           RETURN s
           """)
    Mono<Sale> updateSaleForContado(@Param("saleId") UUID saleId);

    /**
     * Actualiza los campos del Sale para iniciar venta a crédito.
     * Se usa después de calcular totales y crear CobroDineroProgramado con repositorios específicos.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           SET s.tipoVenta = 'CREDITO',
               s.estaPagadoEntregado = false,
               s.totalRestante = s.totalMontoAcordado
           RETURN s
           """)
    Mono<Sale> updateSaleForCredito(@Param("saleId") UUID saleId);

    /**
     * Actualiza los campos del Sale para iniciar venta como pedido.
     * Se usa después de calcular totales y crear CobroDineroProgramado con repositorios específicos.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           SET s.tipoVenta = 'PEDIDO',
               s.estaPagadoEntregado = false,
               s.totalRestante = s.totalMontoAcordado
           RETURN s
           """)
    Mono<Sale> updateSaleForPedido(@Param("saleId") UUID saleId);

    /**
     * Crea relación entre Sale y CobroDineroProgramado.
     * Usado después de crear el CobroDineroProgramado con el repositorio específico.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           MATCH (cdp:CobroDineroProgramado) WHERE cdp.id = $cobroDineroProgramadoId
           CREATE (s)-[:CON_DINERO_COBRADO]->(cdp)
           RETURN cdp.id
           """)
    Mono<UUID> createSaleCobroDineroProgramadoRelationship(@Param("saleId") UUID saleId, @Param("cobroDineroProgramadoId") UUID cobroDineroProgramadoId);

    /**
     * Obtiene el Item asociado a un BienServicioCargado.
     * Usado para crear la relación CON_ITEM_DEVUELTO en BienServicioDevuelto.
     */
    @Query("""
           MATCH (bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE bsc.id = $bienServicioCargadoId
           RETURN i.codCompuesto
           """)
    Mono<String> getItemCodCompuestoByBienServicioCargadoId(@Param("bienServicioCargadoId") UUID bienServicioCargadoId);

    /**
     * Crea relación entre Sale y BienServicioDevuelto.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           MATCH (bsd:BienServicioDevuelto) WHERE bsd.id = $bienServicioDevueltoId
           CREATE (s)-[:CON_BIEN_SERVICIO_DEVUELTO]->(bsd)
           RETURN bsd.id
           """)
    Mono<UUID> createBienServicioDevueltoRelationship(@Param("saleId") UUID saleId, @Param("bienServicioDevueltoId") UUID bienServicioDevueltoId);

    /**
     * Crea relación entre BienServicioDevuelto e Item del BienServicioCargado.
     */
    @Query("""
           MATCH (bsd:BienServicioDevuelto) WHERE bsd.id = $bienServicioDevueltoId
           MATCH (bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item) WHERE bsc.id = $bienServicioCargadoId
           CREATE (bsd)-[:CON_ITEM_DEVUELTO]->(i)
           RETURN i.id
           """)
    Mono<UUID> createBienServicioDevueltoItemRelationship(@Param("bienServicioDevueltoId") UUID bienServicioDevueltoId, @Param("bienServicioCargadoId") UUID bienServicioCargadoId);

    /**
     * Crea relación entre Sale y DevolucionDinero.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           MATCH (dd:DevolucionDinero) WHERE dd.id = $devolucionDineroId
           CREATE (s)-[:CON_DINERO_DEVUELTO]->(dd)
           RETURN dd.id
           """)
    Mono<UUID> createDevolucionDineroRelationship(@Param("saleId") UUID saleId, @Param("devolucionDineroId") UUID devolucionDineroId);

    /**
     * Encuentra todas las ventas de tipo CONTADO que no están pagadas/entregadas.
     * Ordena por fecha de creación, de más antiguo a más reciente.
     */
    @Query("""
       MATCH (s:Sale)
       WHERE s.tipoVenta = 'CONTADO' AND s.estaPagadoEntregado = false
       OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
       OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
       OPTIONAL MATCH (bsc)-[:CON_ITEM_CARGADO]->(i:Item)
       OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_DEVUELTO]->(bsd:BienServicioDevuelto)
       OPTIONAL MATCH (s)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
       OPTIONAL MATCH (s)-[:CON_DINERO_DEVUELTO]->(dd:DevolucionDinero)
       RETURN s, c, bsc, i, bsd, cdp, dd
       ORDER BY s.createdAt ASC
       """)
    Flux<Sale> findSalesByTipoVentaContadoAndNotPagadoEntregado();

    /**
     * Encuentra todas las ventas de tipo CREDITO que no están pagadas/entregadas.
     * Ordena por fecha de creación, de más antiguo a más reciente.
     */
    @Query("""
           MATCH (s:Sale)
           WHERE s.tipoVenta = 'CREDITO' AND s.estaPagadoEntregado = false
           RETURN s
           ORDER BY s.createdAt ASC
           """)
    Flux<Sale> findSalesByTipoVentaCreditoAndNotPagadoEntregado();

    /**
     * Encuentra todas las ventas de tipo PEDIDO que no están pagadas/entregadas.
     * Ordena por fecha de creación, de más antiguo a más reciente.
     */
    @Query("""
           MATCH (s:Sale)
           WHERE s.tipoVenta = 'PEDIDO' AND s.estaPagadoEntregado = false
           RETURN s
           ORDER BY s.createdAt ASC
           """)
    Flux<Sale> findSalesByTipoVentaPedidoAndNotPagadoEntregado();

    /**
     * Actualiza solo los campos totalRestante y estaPagadoEntregado de un Sale.
     * Este método preserva todas las relaciones existentes del Sale al actualizar
     * únicamente los campos especificados sin sobreescribir el nodo completo.
     *
     * @param saleId ID del Sale a actualizar
     * @param totalRestante Nuevo valor para totalRestante
     * @param estaPagadoEntregado Nuevo valor para estaPagadoEntregado
     * @return Mono<UUID> ID del Sale actualizado
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           SET s.totalRestante = $totalRestante,
               s.estaPagadoEntregado = $estaPagadoEntregado
           RETURN s.id
           """)
    Mono<UUID> updateSaleTotalRestanteAndEstaPagadoEntregado(
            @Param("saleId") UUID saleId,
            @Param("totalRestante") Double totalRestante,
            @Param("estaPagadoEntregado") Boolean estaPagadoEntregado);

    /**
     * Obtiene todas las ventas que no están pagadas ni entregadas (`estaPagadoEntregado = false`)
     * y cuyo tipo de venta no es 'PROFORMA', ordenadas por fecha de creación de más reciente a más antiguo.
     * Esta consulta está optimizada para suscripciones en tiempo real, retornando solo
     * los campos básicos del nodo `Sale` sin cargar relaciones.
     *
     * @return Flux<Sale> Lista de ventas pendientes (excluyendo proformas) ordenadas por createdAt DESC
     */
    @Query("""
           MATCH (s:Sale)
           WHERE s.estaPagadoEntregado = false AND s.tipoVenta <> 'PROFORMA'
           RETURN s
           ORDER BY s.createdAt DESC
           """)
    Flux<Sale> findSalesPendientesPorCobrar();

    /**
     * Busca Sales completadas (estaPagadoEntregado = true) con filtros opcionales.
     * Si todos los parámetros son null/vacíos, retorna las últimas 99 ventas completadas.
     * Si hay filtros, retorna máximo 33 resultados que coincidan con los criterios.
     *
     * @param saleId ID específico del Sale (opcional)
     * @param iniciadaPorId ID del User que inició la venta (opcional)
     * @param datoDocumento DNI, RUC u otro documento del Cliente (opcional)
     * @param nombreCliente Nombre, apellido o razón social del Cliente (búsqueda con CONTAINS, opcional)
     * @param codCompuesto Código compuesto del Item en BienServicioCargado o BienServicioDevuelto (búsqueda con CONTAINS, opcional)
     * @param tipoVenta Tipo de venta (opcional)
     * @return Flux<Sale> Lista de Sales que coinciden con los filtros, ordenados por createdAt DESC
     */
    @Query("""
           MATCH (s:Sale)
           WHERE s.estaPagadoEntregado = true
           // Filtro por ID específico del Sale
           AND ($saleId IS NULL OR s.id = $saleId)
           // Filtro por User que inició la venta
           AND ($iniciadaPorId IS NULL OR EXISTS {
               MATCH (s)-[:INICIADA_POR]->(u:User)
               WHERE u.id = $iniciadaPorId
           })
           // Filtro por tipo de venta
           AND ($tipoVenta IS NULL OR s.tipoVenta = $tipoVenta)
           // Filtro por datos de documento del Cliente (DNI, RUC, otroDocumento)
           AND ($datoDocumento IS NULL OR $datoDocumento = '' OR EXISTS {
               MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
               WHERE c.dni = $datoDocumento OR c.ruc = $datoDocumento OR c.otroDocumento = $datoDocumento
           })
           // Filtro por nombre/apellido/razónSocial del Cliente (búsqueda con CONTAINS)
           AND ($nombreCliente IS NULL OR $nombreCliente = '' OR EXISTS {
               MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
               WHERE toLower(c.nombre) CONTAINS toLower($nombreCliente)
                  OR toLower(c.apellido) CONTAINS toLower($nombreCliente)
                  OR toLower(c.razonSocial) CONTAINS toLower($nombreCliente)
           })
           // Filtro por código compuesto del Item (en BienServicioCargado o BienServicioDevuelto)
           AND ($codCompuesto IS NULL OR $codCompuesto = '' OR EXISTS {
               MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
               WHERE toLower(i.codCompuesto) CONTAINS toLower($codCompuesto)
           } OR EXISTS {
               MATCH (s)-[:CON_BIEN_SERVICIO_DEVUELTO]->(bsd:BienServicioDevuelto)-[:CON_ITEM_DEVUELTO]->(i:Item)
               WHERE toLower(i.codCompuesto) CONTAINS toLower($codCompuesto)
           })
           RETURN s
           ORDER BY s.createdAt DESC
           // Limitar resultados: 33 si hay filtros, 99 si no hay filtros
           LIMIT CASE
               WHEN $saleId IS NOT NULL OR $iniciadaPorId IS NOT NULL OR
                    ($datoDocumento IS NOT NULL AND $datoDocumento <> '') OR
                    ($nombreCliente IS NOT NULL AND $nombreCliente <> '') OR
                    ($codCompuesto IS NOT NULL AND $codCompuesto <> '') OR
                    $tipoVenta IS NOT NULL
               THEN 33
               ELSE 99
           END
           """)
    Flux<Sale> findSalesCompletadasConFiltros(
            @Param("saleId") UUID saleId,
            @Param("iniciadaPorId") UUID iniciadaPorId,
            @Param("datoDocumento") String datoDocumento,
            @Param("nombreCliente") String nombreCliente,
            @Param("codCompuesto") String codCompuesto,
            @Param("tipoVenta") String tipoVenta);
}
